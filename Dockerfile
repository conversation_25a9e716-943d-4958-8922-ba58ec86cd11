# Use an official Node.js runtime as a parent image
FROM node:18

# Install pnpm globally
RUN npm install -g pnpm

# Set the working directory in the container
WORKDIR /app

# Copy package.json and pnpm-lock.yaml to leverage Docker cache
# This layer will be cached and only rebuilt if these files change
COPY package.json pnpm-lock.yaml ./

# Install application dependencies using pnpm
RUN pnpm install --frozen-lockfile

# Copy the rest of the application source code into the container
COPY . .

# Expose the port the app runs on (matching app.js port 3000)
EXPOSE 3000

# Run the application
CMD [ "node", "app.js" ]
