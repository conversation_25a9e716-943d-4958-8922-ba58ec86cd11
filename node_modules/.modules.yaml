hoistPattern:
  - '*'
hoistedDependencies:
  '@fastify/ajv-compiler@3.6.0':
    '@fastify/ajv-compiler': private
  '@fastify/error@3.4.1':
    '@fastify/error': private
  '@fastify/fast-json-stringify-compiler@4.3.0':
    '@fastify/fast-json-stringify-compiler': private
  '@fastify/merge-json-schemas@0.1.1':
    '@fastify/merge-json-schemas': private
  abstract-logging@2.0.1:
    abstract-logging: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv@8.17.1:
    ajv: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  avvio@8.4.0:
    avvio: private
  cookie@0.7.2:
    cookie: private
  fast-content-type-parse@1.1.0:
    fast-content-type-parse: private
  fast-decode-uri-component@1.0.1:
    fast-decode-uri-component: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stringify@5.16.1:
    fast-json-stringify: private
  fast-querystring@1.1.2:
    fast-querystring: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-uri@2.4.0:
    fast-uri: private
  fastify-plugin@4.5.1:
    fastify-plugin: private
  fastq@1.19.1:
    fastq: private
  find-my-way@8.2.2:
    find-my-way: private
  forwarded@0.2.0:
    forwarded: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  json-schema-ref-resolver@1.0.1:
    json-schema-ref-resolver: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  light-my-request@5.14.0:
    light-my-request: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pg@8.16.3:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.9.0:
    pino: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  process-warning@3.0.0:
    process-warning: private
  proxy-addr@2.0.7:
    proxy-addr: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  real-require@0.2.0:
    real-require: private
  require-from-string@2.0.2:
    require-from-string: private
  ret@0.4.3:
    ret: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  safe-regex2@3.1.0:
    safe-regex2: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.2:
    semver: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  sonic-boom@4.2.0:
    sonic-boom: private
  split2@4.2.0:
    split2: private
  thread-stream@3.1.0:
    thread-stream: private
  toad-cache@3.7.0:
    toad-cache: private
  xtend@4.0.2:
    xtend: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Tue, 02 Sep 2025 11:02:44 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
