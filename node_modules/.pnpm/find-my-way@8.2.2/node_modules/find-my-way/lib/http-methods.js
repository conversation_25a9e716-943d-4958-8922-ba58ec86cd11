'use strict'

// defined by Node.js http module, a snapshot from Node.js 18.12.0
const httpMethods = [
  'ACL', 'BIND', 'CHECKOUT', 'CONNECT', 'COPY', 'DELETE',
  'GET', 'HEAD', 'LINK', 'LOCK', '<PERSON>-<PERSON><PERSON><PERSON>', '<PERSON>R<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>VI<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'MOVE', 'NOTIFY', 'OPTIONS',
  'PATCH', 'POST', 'PROPFIND', 'PROPPATCH', 'PURGE', 'PUT',
  'REBIND', 'REPORT', 'SEARCH', 'SOURCE', 'SUBSCRIBE', 'TRACE',
  '<PERSON>BI<PERSON>', '<PERSON>LINK', 'UNLOCK', 'UNSUBSCRIBE'
]

module.exports = httpMethods
