{"name": "find-my-way", "version": "8.2.2", "description": "Crazy fast http radix based router", "main": "index.js", "types": "index.d.ts", "scripts": {"bench": "node ./benchmark/bench.js", "bench:cmp": "node ./benchmark/compare-branches.js", "bench:cmp:ci": "node ./benchmark/compare-branches.js --ci", "test:lint": "standard", "test:typescript": "tsd", "test": "standard && tap -J test/*.test.js && npm run test:typescript", "test:report": "tap -J test/*.test.js --cov --coverage-report=html --coverage-report=cobertura | tee out.tap", "test:reporter": "tap-mocha-reporter xunit < out.tap > test/junit-testresults.xml"}, "repository": {"type": "git", "url": "git+https://github.com/delvedor/find-my-way.git"}, "keywords": ["http", "router", "radix", "fast", "speed"], "engines": {"node": ">=14"}, "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/delvedor/find-my-way/issues"}, "homepage": "https://github.com/delvedor/find-my-way#readme", "devDependencies": {"@types/node": "^14.0.27", "benchmark": "^2.1.4", "chalk": "^4.1.2", "inquirer": "^8.2.4", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "rfdc": "^1.3.0", "simple-git": "^3.7.1", "standard": "^14.3.4", "tap": "^16.0.1", "tap-mocha-reporter": "^5.0.1", "tsd": "^0.13.1"}, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-querystring": "^1.0.0", "safe-regex2": "^3.1.0"}, "tsd": {"directory": "test/types"}}