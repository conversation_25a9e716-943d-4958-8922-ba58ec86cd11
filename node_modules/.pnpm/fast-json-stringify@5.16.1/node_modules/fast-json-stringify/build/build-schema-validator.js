'use strict'

const Ajv = require('ajv')
const standaloneCode = require('ajv/dist/standalone').default
const ajvFormats = require('ajv-formats')
const fs = require('fs')
const path = require('path')

const ajv = new Ajv({
  addUsedSchema: false,
  allowUnionTypes: true,
  code: {
    source: true,
    lines: true,
    optimize: 3
  }
})
ajvFormats(ajv)

const schema = require('ajv/lib/refs/json-schema-draft-07.json')
const validate = ajv.compile(schema)
const validationCode = standaloneCode(ajv, validate)

const moduleCode = `/* CODE GENERATED BY '${path.basename(__filename)}' DO NOT EDIT! */\n${validationCode}`

fs.writeFileSync(path.join(__dirname, '../lib/schema-validator.js'), moduleCode)
