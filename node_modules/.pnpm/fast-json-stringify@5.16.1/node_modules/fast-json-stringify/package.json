{"name": "fast-json-stringify", "version": "5.16.1", "description": "Stringify your JSON at max speed", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"bench": "node ./benchmark/bench.js", "bench:cmp": "node ./benchmark/bench-cmp-branch.js", "bench:cmp:ci": "node ./benchmark/bench-cmp-branch.js --ci", "benchmark": "node ./benchmark/bench-cmp-lib.js", "lint": "standard", "lint:fix": "standard --fix", "test:typescript": "tsd", "test:unit": "tap", "test": "npm run test:unit && npm run test:typescript"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-json-stringify.git"}, "keywords": ["json", "stringify", "schema", "fast"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fast-json-stringify/issues"}, "homepage": "https://github.com/fastify/fast-json-stringify#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@sinclair/typebox": "^0.32.3", "benchmark": "^2.1.4", "cli-select": "^1.1.2", "compile-json-stringify": "^0.1.2", "is-my-json-valid": "^2.20.0", "simple-git": "^3.7.1", "standard": "^17.0.0", "tap": "^16.0.1", "tsd": "^0.31.0", "webpack": "^5.40.0", "fast-json-stringify": "."}, "dependencies": {"ajv": "^8.10.0", "ajv-formats": "^3.0.1", "fast-deep-equal": "^3.1.3", "fast-uri": "^2.1.0", "rfdc": "^1.2.0", "json-schema-ref-resolver": "^1.0.1", "@fastify/merge-json-schemas": "^0.1.0"}, "standard": {"ignore": ["schema-validator.js"]}, "runkitExampleFilename": "./examples/example.js"}