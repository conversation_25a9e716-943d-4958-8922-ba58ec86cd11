'use strict'

// Internal Symbols
const kAvvio = Symbol('avvio.Boot')
const kIsOnCloseHandler = Symbol('isOnCloseHandler')
const kThenifyDoNotWrap = Symbol('avvio.ThenifyDoNotWrap')
const kUntrackNode = Symbol('avvio.TimeTree.untrackNode')
const kTrackNode = Symbol('avvio.TimeTree.trackNode')
const kGetParent = Symbol('avvio.TimeTree.getParent')
const kGetNode = Symbol('avvio.TimeTree.getNode')
const kAddNode = Symbol('avvio.TimeTree.addNode')

// Public Symbols
const kPluginMeta = Symbol.for('plugin-meta')

module.exports = {
  kAvvio,
  kIsOnCloseHandler,
  kThenifyDoNotWrap,
  kUntrackNode,
  kTrackNode,
  kGetParent,
  kGetNode,
  kAddNode,
  kPluginMeta
}
