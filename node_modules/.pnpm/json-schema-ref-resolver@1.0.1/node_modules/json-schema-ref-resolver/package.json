{"name": "json-schema-ref-resolver", "version": "1.0.1", "description": "JSON schema reference resolver", "main": "index.js", "types": "types/index.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "test:unit": "c8 --100 node --test", "test:typescript": "tsd", "test": "npm run lint && npm run test:unit && npm run test:typescript"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/fastify/json-schema-ref-resolver.git"}, "keywords": ["json", "schema", "reference", "openapi", "swagger"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "devDependencies": {"c8": "^8.0.1", "standard": "^17.1.0", "tsd": "^0.29.0"}, "standard": {"ignore": ["types/*"]}}