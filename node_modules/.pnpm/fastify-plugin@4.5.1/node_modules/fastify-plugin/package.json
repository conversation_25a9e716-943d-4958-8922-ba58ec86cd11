{"name": "fastify-plugin", "version": "4.5.1", "description": "Plugin helper for Fastify", "main": "plugin.js", "types": "types/plugin.d.ts", "scripts": {"lint": "standard", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-plugin.git"}, "keywords": ["plugin", "helper", "fastify"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-plugin/issues"}, "homepage": "https://github.com/fastify/fastify-plugin#readme", "devDependencies": {"@fastify/type-provider-typebox": "^3.0.0", "@types/node": "^20.1.0", "fastify": "^4.0.1", "proxyquire": "^2.1.3", "standard": "^17.0.0", "tap": "^16.0.1", "tsd": "^0.28.0"}}