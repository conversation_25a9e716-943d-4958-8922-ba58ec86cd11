{"name": "@fastify/postgres", "version": "5.2.2", "description": "Fastify PostgreSQL connection plugin", "main": "index.js", "types": "index.d.ts", "scripts": {"check-examples": "tsc --build examples/typescript/*", "lint": "standard", "lint:fix": "standard --fix", "load-data": "docker exec -it fastify-postgres psql -c 'CREATE TABLE users(id serial PRIMARY KEY, username VARCHAR (50) NOT NULL);' -U postgres -d postgres", "postgres": "docker run -p 5432:5432 --name fastify-postgres -e POSTGRES_PASSWORD=postgres -d postgres:11-alpine", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap -J test/*.test.js", "test:report": "standard && tap -J --coverage-report=html test/*.test.js", "test:typescript": "tsd", "test:verbose": "standard && tap -J test/*.test.js -Rspec"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-postgres.git"}, "keywords": ["fastify", "postgres", "postgresql", "database", "connection", "sql"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-postgres/issues"}, "homepage": "https://github.com/fastify/fastify-postgres#readme", "dependencies": {"fastify-plugin": "^4.0.0"}, "devDependencies": {"@tsconfig/node10": "^1.0.8", "@types/pg": "^8.6.1", "fastify": "^4.0.0-rc.2", "pg": "^8.7.1", "pg-native": "^3.0.0", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.29.0", "typescript": "^5.0.2"}, "peerDependencies": {"pg": ">=6.0.0"}, "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}