<h1 align="center">Fastify</h1>

## Guides Table Of Contents
<a id="guides-toc"></a>

This table of contents is in alphabetical order.

+ [Benchmarking](./Benchmarking.md): This guide introduces how to benchmark
  applications based on Fastify.
+ [Contributing](./Contributing.md): Details how to participate in the
  development of Fastify, and shows how to setup an environment compatible with
  the project's code style.
+ [Delay Accepting Requests](./Delay-Accepting-Requests.md): A practical guide
  on how to delay serving requests to specific routes until some condition is
  met in your application. This guide focuses on solving the problem using
  [`Hooks`](../Reference/Hooks.md), [`Decorators`](../Reference/Decorators.md),
  and [`Plugins`](../Reference/Plugins.md).
+ [Detecting When Clients Abort](./Detecting-When-Clients-Abort.md): A 
  practical guide on detecting if and when a client aborts a request.
+ [Ecosystem](./Ecosystem.md): Lists all core plugins and many known community
  plugins.
+ [Fluent Schema](./Fluent-Schema.md): Shows how writing JSON Schema can be
  written with a fluent API and used in Fastify.
+ [Getting Started](./Getting-Started.md): Introduction tutorial for Fastify.
  This is where beginners should start.
+ [Migration Guide (v4)](./Migration-Guide-V4.md): Details how to migrate to
  Fastify v4 from earlier versions.
+ [Migration Guide (v3)](./Migration-Guide-V3.md): Details how to migrate to
  Fastify v3 from earlier versions.
+ [Plugins Guide](./Plugins-Guide.md): An informal introduction to writing
  Fastify plugins.
+ [Prototype Poisoning](./Prototype-Poisoning.md): A description of how the
  prototype poisoning attack works and is mitigated.
+ [Recommendations](./Recommendations.md): Recommendations for how to deploy
  Fastify into production environments.
+ [Serverless](./Serverless.md): Details on how to deploy Fastify applications
  in various Function as a Service (FaaS) environments.
+ [Style Guide](./Style-Guide.md): Explains the writing style we use for the
  Fastify documentation for those who want to contribute documentation.
+ [Testing](./Testing.md): Explains how to write unit tests for Fastify
  applications.
+ [Write Plugin](./Write-Plugin.md): A set of guidelines for what the Fastify
  team considers good practices for writing a Fastify plugin.
