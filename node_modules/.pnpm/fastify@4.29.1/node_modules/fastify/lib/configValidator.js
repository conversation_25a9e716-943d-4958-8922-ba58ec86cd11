// This file is autogenerated by build/build-validation.js, do not edit
/* istanbul ignore file */
"use strict";
module.exports = validate10;
module.exports.default = validate10;
const schema11 = {"type":"object","additionalProperties":false,"properties":{"connectionTimeout":{"type":"integer","default":0},"keepAliveTimeout":{"type":"integer","default":72000},"forceCloseConnections":{"oneOf":[{"type":"string","pattern":"idle"},{"type":"boolean"}]},"maxRequestsPerSocket":{"type":"integer","default":0,"nullable":true},"requestTimeout":{"type":"integer","default":0},"bodyLimit":{"type":"integer","default":1048576},"caseSensitive":{"type":"boolean","default":true},"allowUnsafeRegex":{"type":"boolean","default":false},"http2":{"type":"boolean"},"https":{"if":{"not":{"oneOf":[{"type":"boolean"},{"type":"null"},{"type":"object","additionalProperties":false,"required":["allowHTTP1"],"properties":{"allowHTTP1":{"type":"boolean"}}}]}},"then":{"setDefaultValue":true}},"ignoreTrailingSlash":{"type":"boolean","default":false},"ignoreDuplicateSlashes":{"type":"boolean","default":false},"disableRequestLogging":{"type":"boolean","default":false},"jsonShorthand":{"type":"boolean","default":true},"maxParamLength":{"type":"integer","default":100},"onProtoPoisoning":{"type":"string","default":"error"},"onConstructorPoisoning":{"type":"string","default":"error"},"pluginTimeout":{"type":"integer","default":10000},"requestIdHeader":{"anyOf":[{"enum":[false]},{"type":"string"}],"default":"request-id"},"requestIdLogLabel":{"type":"string","default":"reqId"},"http2SessionTimeout":{"type":"integer","default":72000},"exposeHeadRoutes":{"type":"boolean","default":true},"useSemicolonDelimiter":{"type":"boolean","default":true},"versioning":{"type":"object","additionalProperties":true,"required":["storage","deriveVersion"],"properties":{"storage":{},"deriveVersion":{}}},"constraints":{"type":"object","additionalProperties":{"type":"object","required":["name","storage","validate","deriveConstraint"],"additionalProperties":true,"properties":{"name":{"type":"string"},"storage":{},"validate":{},"deriveConstraint":{}}}}}};
const func2 = Object.prototype.hasOwnProperty;
const pattern0 = new RegExp("idle", "u");

function validate10(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){
let vErrors = null;
let errors = 0;
if(errors === 0){
if(data && typeof data == "object" && !Array.isArray(data)){
if(data.connectionTimeout === undefined){
data.connectionTimeout = 0;
}
if(data.keepAliveTimeout === undefined){
data.keepAliveTimeout = 72000;
}
if(data.maxRequestsPerSocket === undefined){
data.maxRequestsPerSocket = 0;
}
if(data.requestTimeout === undefined){
data.requestTimeout = 0;
}
if(data.bodyLimit === undefined){
data.bodyLimit = 1048576;
}
if(data.caseSensitive === undefined){
data.caseSensitive = true;
}
if(data.allowUnsafeRegex === undefined){
data.allowUnsafeRegex = false;
}
if(data.ignoreTrailingSlash === undefined){
data.ignoreTrailingSlash = false;
}
if(data.ignoreDuplicateSlashes === undefined){
data.ignoreDuplicateSlashes = false;
}
if(data.disableRequestLogging === undefined){
data.disableRequestLogging = false;
}
if(data.jsonShorthand === undefined){
data.jsonShorthand = true;
}
if(data.maxParamLength === undefined){
data.maxParamLength = 100;
}
if(data.onProtoPoisoning === undefined){
data.onProtoPoisoning = "error";
}
if(data.onConstructorPoisoning === undefined){
data.onConstructorPoisoning = "error";
}
if(data.pluginTimeout === undefined){
data.pluginTimeout = 10000;
}
if(data.requestIdHeader === undefined){
data.requestIdHeader = "request-id";
}
if(data.requestIdLogLabel === undefined){
data.requestIdLogLabel = "reqId";
}
if(data.http2SessionTimeout === undefined){
data.http2SessionTimeout = 72000;
}
if(data.exposeHeadRoutes === undefined){
data.exposeHeadRoutes = true;
}
if(data.useSemicolonDelimiter === undefined){
data.useSemicolonDelimiter = true;
}
const _errs1 = errors;
for(const key0 in data){
if(!(func2.call(schema11.properties, key0))){
delete data[key0];
}
}
if(_errs1 === errors){
let data0 = data.connectionTimeout;
const _errs2 = errors;
if(!(((typeof data0 == "number") && (!(data0 % 1) && !isNaN(data0))) && (isFinite(data0)))){
let dataType0 = typeof data0;
let coerced0 = undefined;
if(!(coerced0 !== undefined)){
if(dataType0 === "boolean" || data0 === null
              || (dataType0 === "string" && data0 && data0 == +data0 && !(data0 % 1))){
coerced0 = +data0;
}
else {
validate10.errors = [{instancePath:instancePath+"/connectionTimeout",schemaPath:"#/properties/connectionTimeout/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced0 !== undefined){
data0 = coerced0;
if(data !== undefined){
data["connectionTimeout"] = coerced0;
}
}
}
var valid0 = _errs2 === errors;
if(valid0){
let data1 = data.keepAliveTimeout;
const _errs4 = errors;
if(!(((typeof data1 == "number") && (!(data1 % 1) && !isNaN(data1))) && (isFinite(data1)))){
let dataType1 = typeof data1;
let coerced1 = undefined;
if(!(coerced1 !== undefined)){
if(dataType1 === "boolean" || data1 === null
              || (dataType1 === "string" && data1 && data1 == +data1 && !(data1 % 1))){
coerced1 = +data1;
}
else {
validate10.errors = [{instancePath:instancePath+"/keepAliveTimeout",schemaPath:"#/properties/keepAliveTimeout/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced1 !== undefined){
data1 = coerced1;
if(data !== undefined){
data["keepAliveTimeout"] = coerced1;
}
}
}
var valid0 = _errs4 === errors;
if(valid0){
if(data.forceCloseConnections !== undefined){
let data2 = data.forceCloseConnections;
const _errs6 = errors;
const _errs7 = errors;
let valid1 = false;
let passing0 = null;
const _errs8 = errors;
if(typeof data2 !== "string"){
let dataType2 = typeof data2;
let coerced2 = undefined;
if(!(coerced2 !== undefined)){
if(dataType2 == "number" || dataType2 == "boolean"){
coerced2 = "" + data2;
}
else if(data2 === null){
coerced2 = "";
}
else {
const err0 = {instancePath:instancePath+"/forceCloseConnections",schemaPath:"#/properties/forceCloseConnections/oneOf/0/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err0];
}
else {
vErrors.push(err0);
}
errors++;
}
}
if(coerced2 !== undefined){
data2 = coerced2;
if(data !== undefined){
data["forceCloseConnections"] = coerced2;
}
}
}
if(errors === _errs8){
if(typeof data2 === "string"){
if(!pattern0.test(data2)){
const err1 = {instancePath:instancePath+"/forceCloseConnections",schemaPath:"#/properties/forceCloseConnections/oneOf/0/pattern",keyword:"pattern",params:{pattern: "idle"},message:"must match pattern \""+"idle"+"\""};
if(vErrors === null){
vErrors = [err1];
}
else {
vErrors.push(err1);
}
errors++;
}
}
}
var _valid0 = _errs8 === errors;
if(_valid0){
valid1 = true;
passing0 = 0;
}
const _errs10 = errors;
if(typeof data2 !== "boolean"){
let coerced3 = undefined;
if(!(coerced3 !== undefined)){
if(data2 === "false" || data2 === 0 || data2 === null){
coerced3 = false;
}
else if(data2 === "true" || data2 === 1){
coerced3 = true;
}
else {
const err2 = {instancePath:instancePath+"/forceCloseConnections",schemaPath:"#/properties/forceCloseConnections/oneOf/1/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"};
if(vErrors === null){
vErrors = [err2];
}
else {
vErrors.push(err2);
}
errors++;
}
}
if(coerced3 !== undefined){
data2 = coerced3;
if(data !== undefined){
data["forceCloseConnections"] = coerced3;
}
}
}
var _valid0 = _errs10 === errors;
if(_valid0 && valid1){
valid1 = false;
passing0 = [passing0, 1];
}
else {
if(_valid0){
valid1 = true;
passing0 = 1;
}
}
if(!valid1){
const err3 = {instancePath:instancePath+"/forceCloseConnections",schemaPath:"#/properties/forceCloseConnections/oneOf",keyword:"oneOf",params:{passingSchemas: passing0},message:"must match exactly one schema in oneOf"};
if(vErrors === null){
vErrors = [err3];
}
else {
vErrors.push(err3);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs7;
if(vErrors !== null){
if(_errs7){
vErrors.length = _errs7;
}
else {
vErrors = null;
}
}
}
var valid0 = _errs6 === errors;
}
else {
var valid0 = true;
}
if(valid0){
let data3 = data.maxRequestsPerSocket;
const _errs12 = errors;
if((!(((typeof data3 == "number") && (!(data3 % 1) && !isNaN(data3))) && (isFinite(data3)))) && (data3 !== null)){
let dataType4 = typeof data3;
let coerced4 = undefined;
if(!(coerced4 !== undefined)){
if(dataType4 === "boolean" || data3 === null
              || (dataType4 === "string" && data3 && data3 == +data3 && !(data3 % 1))){
coerced4 = +data3;
}
else if(data3 === "" || data3 === 0 || data3 === false){
coerced4 = null;
}
else {
validate10.errors = [{instancePath:instancePath+"/maxRequestsPerSocket",schemaPath:"#/properties/maxRequestsPerSocket/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced4 !== undefined){
data3 = coerced4;
if(data !== undefined){
data["maxRequestsPerSocket"] = coerced4;
}
}
}
var valid0 = _errs12 === errors;
if(valid0){
let data4 = data.requestTimeout;
const _errs15 = errors;
if(!(((typeof data4 == "number") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))){
let dataType5 = typeof data4;
let coerced5 = undefined;
if(!(coerced5 !== undefined)){
if(dataType5 === "boolean" || data4 === null
              || (dataType5 === "string" && data4 && data4 == +data4 && !(data4 % 1))){
coerced5 = +data4;
}
else {
validate10.errors = [{instancePath:instancePath+"/requestTimeout",schemaPath:"#/properties/requestTimeout/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced5 !== undefined){
data4 = coerced5;
if(data !== undefined){
data["requestTimeout"] = coerced5;
}
}
}
var valid0 = _errs15 === errors;
if(valid0){
let data5 = data.bodyLimit;
const _errs17 = errors;
if(!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))){
let dataType6 = typeof data5;
let coerced6 = undefined;
if(!(coerced6 !== undefined)){
if(dataType6 === "boolean" || data5 === null
              || (dataType6 === "string" && data5 && data5 == +data5 && !(data5 % 1))){
coerced6 = +data5;
}
else {
validate10.errors = [{instancePath:instancePath+"/bodyLimit",schemaPath:"#/properties/bodyLimit/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced6 !== undefined){
data5 = coerced6;
if(data !== undefined){
data["bodyLimit"] = coerced6;
}
}
}
var valid0 = _errs17 === errors;
if(valid0){
let data6 = data.caseSensitive;
const _errs19 = errors;
if(typeof data6 !== "boolean"){
let coerced7 = undefined;
if(!(coerced7 !== undefined)){
if(data6 === "false" || data6 === 0 || data6 === null){
coerced7 = false;
}
else if(data6 === "true" || data6 === 1){
coerced7 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/caseSensitive",schemaPath:"#/properties/caseSensitive/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced7 !== undefined){
data6 = coerced7;
if(data !== undefined){
data["caseSensitive"] = coerced7;
}
}
}
var valid0 = _errs19 === errors;
if(valid0){
let data7 = data.allowUnsafeRegex;
const _errs21 = errors;
if(typeof data7 !== "boolean"){
let coerced8 = undefined;
if(!(coerced8 !== undefined)){
if(data7 === "false" || data7 === 0 || data7 === null){
coerced8 = false;
}
else if(data7 === "true" || data7 === 1){
coerced8 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/allowUnsafeRegex",schemaPath:"#/properties/allowUnsafeRegex/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced8 !== undefined){
data7 = coerced8;
if(data !== undefined){
data["allowUnsafeRegex"] = coerced8;
}
}
}
var valid0 = _errs21 === errors;
if(valid0){
if(data.http2 !== undefined){
let data8 = data.http2;
const _errs23 = errors;
if(typeof data8 !== "boolean"){
let coerced9 = undefined;
if(!(coerced9 !== undefined)){
if(data8 === "false" || data8 === 0 || data8 === null){
coerced9 = false;
}
else if(data8 === "true" || data8 === 1){
coerced9 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/http2",schemaPath:"#/properties/http2/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced9 !== undefined){
data8 = coerced9;
if(data !== undefined){
data["http2"] = coerced9;
}
}
}
var valid0 = _errs23 === errors;
}
else {
var valid0 = true;
}
if(valid0){
if(data.https !== undefined){
let data9 = data.https;
const _errs25 = errors;
const _errs26 = errors;
let valid2 = true;
const _errs27 = errors;
const _errs28 = errors;
const _errs29 = errors;
const _errs30 = errors;
let valid4 = false;
let passing1 = null;
const _errs31 = errors;
if(typeof data9 !== "boolean"){
let coerced10 = undefined;
if(!(coerced10 !== undefined)){
if(data9 === "false" || data9 === 0 || data9 === null){
coerced10 = false;
}
else if(data9 === "true" || data9 === 1){
coerced10 = true;
}
else {
const err4 = {};
if(vErrors === null){
vErrors = [err4];
}
else {
vErrors.push(err4);
}
errors++;
}
}
if(coerced10 !== undefined){
data9 = coerced10;
if(data !== undefined){
data["https"] = coerced10;
}
}
}
var _valid2 = _errs31 === errors;
if(_valid2){
valid4 = true;
passing1 = 0;
}
const _errs33 = errors;
if(data9 !== null){
let coerced11 = undefined;
if(!(coerced11 !== undefined)){
if(data9 === "" || data9 === 0 || data9 === false){
coerced11 = null;
}
else {
const err5 = {};
if(vErrors === null){
vErrors = [err5];
}
else {
vErrors.push(err5);
}
errors++;
}
}
if(coerced11 !== undefined){
data9 = coerced11;
if(data !== undefined){
data["https"] = coerced11;
}
}
}
var _valid2 = _errs33 === errors;
if(_valid2 && valid4){
valid4 = false;
passing1 = [passing1, 1];
}
else {
if(_valid2){
valid4 = true;
passing1 = 1;
}
const _errs35 = errors;
if(errors === _errs35){
if(data9 && typeof data9 == "object" && !Array.isArray(data9)){
let missing0;
if((data9.allowHTTP1 === undefined) && (missing0 = "allowHTTP1")){
const err6 = {};
if(vErrors === null){
vErrors = [err6];
}
else {
vErrors.push(err6);
}
errors++;
}
else {
const _errs37 = errors;
for(const key1 in data9){
if(!(key1 === "allowHTTP1")){
delete data9[key1];
}
}
if(_errs37 === errors){
if(data9.allowHTTP1 !== undefined){
let data10 = data9.allowHTTP1;
if(typeof data10 !== "boolean"){
let coerced12 = undefined;
if(!(coerced12 !== undefined)){
if(data10 === "false" || data10 === 0 || data10 === null){
coerced12 = false;
}
else if(data10 === "true" || data10 === 1){
coerced12 = true;
}
else {
const err7 = {};
if(vErrors === null){
vErrors = [err7];
}
else {
vErrors.push(err7);
}
errors++;
}
}
if(coerced12 !== undefined){
data10 = coerced12;
if(data9 !== undefined){
data9["allowHTTP1"] = coerced12;
}
}
}
}
}
}
}
else {
const err8 = {};
if(vErrors === null){
vErrors = [err8];
}
else {
vErrors.push(err8);
}
errors++;
}
}
var _valid2 = _errs35 === errors;
if(_valid2 && valid4){
valid4 = false;
passing1 = [passing1, 2];
}
else {
if(_valid2){
valid4 = true;
passing1 = 2;
}
}
}
if(!valid4){
const err9 = {};
if(vErrors === null){
vErrors = [err9];
}
else {
vErrors.push(err9);
}
errors++;
}
else {
errors = _errs30;
if(vErrors !== null){
if(_errs30){
vErrors.length = _errs30;
}
else {
vErrors = null;
}
}
}
var valid3 = _errs29 === errors;
if(valid3){
const err10 = {};
if(vErrors === null){
vErrors = [err10];
}
else {
vErrors.push(err10);
}
errors++;
}
else {
errors = _errs28;
if(vErrors !== null){
if(_errs28){
vErrors.length = _errs28;
}
else {
vErrors = null;
}
}
}
var _valid1 = _errs27 === errors;
errors = _errs26;
if(vErrors !== null){
if(_errs26){
vErrors.length = _errs26;
}
else {
vErrors = null;
}
}
if(_valid1){
const _errs40 = errors;
data["https"] = true;
var _valid1 = _errs40 === errors;
valid2 = _valid1;
}
if(!valid2){
const err11 = {instancePath:instancePath+"/https",schemaPath:"#/properties/https/if",keyword:"if",params:{failingKeyword: "then"},message:"must match \"then\" schema"};
if(vErrors === null){
vErrors = [err11];
}
else {
vErrors.push(err11);
}
errors++;
validate10.errors = vErrors;
return false;
}
var valid0 = _errs25 === errors;
}
else {
var valid0 = true;
}
if(valid0){
let data11 = data.ignoreTrailingSlash;
const _errs41 = errors;
if(typeof data11 !== "boolean"){
let coerced13 = undefined;
if(!(coerced13 !== undefined)){
if(data11 === "false" || data11 === 0 || data11 === null){
coerced13 = false;
}
else if(data11 === "true" || data11 === 1){
coerced13 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/ignoreTrailingSlash",schemaPath:"#/properties/ignoreTrailingSlash/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced13 !== undefined){
data11 = coerced13;
if(data !== undefined){
data["ignoreTrailingSlash"] = coerced13;
}
}
}
var valid0 = _errs41 === errors;
if(valid0){
let data12 = data.ignoreDuplicateSlashes;
const _errs43 = errors;
if(typeof data12 !== "boolean"){
let coerced14 = undefined;
if(!(coerced14 !== undefined)){
if(data12 === "false" || data12 === 0 || data12 === null){
coerced14 = false;
}
else if(data12 === "true" || data12 === 1){
coerced14 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/ignoreDuplicateSlashes",schemaPath:"#/properties/ignoreDuplicateSlashes/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced14 !== undefined){
data12 = coerced14;
if(data !== undefined){
data["ignoreDuplicateSlashes"] = coerced14;
}
}
}
var valid0 = _errs43 === errors;
if(valid0){
let data13 = data.disableRequestLogging;
const _errs45 = errors;
if(typeof data13 !== "boolean"){
let coerced15 = undefined;
if(!(coerced15 !== undefined)){
if(data13 === "false" || data13 === 0 || data13 === null){
coerced15 = false;
}
else if(data13 === "true" || data13 === 1){
coerced15 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/disableRequestLogging",schemaPath:"#/properties/disableRequestLogging/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced15 !== undefined){
data13 = coerced15;
if(data !== undefined){
data["disableRequestLogging"] = coerced15;
}
}
}
var valid0 = _errs45 === errors;
if(valid0){
let data14 = data.jsonShorthand;
const _errs47 = errors;
if(typeof data14 !== "boolean"){
let coerced16 = undefined;
if(!(coerced16 !== undefined)){
if(data14 === "false" || data14 === 0 || data14 === null){
coerced16 = false;
}
else if(data14 === "true" || data14 === 1){
coerced16 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/jsonShorthand",schemaPath:"#/properties/jsonShorthand/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced16 !== undefined){
data14 = coerced16;
if(data !== undefined){
data["jsonShorthand"] = coerced16;
}
}
}
var valid0 = _errs47 === errors;
if(valid0){
let data15 = data.maxParamLength;
const _errs49 = errors;
if(!(((typeof data15 == "number") && (!(data15 % 1) && !isNaN(data15))) && (isFinite(data15)))){
let dataType17 = typeof data15;
let coerced17 = undefined;
if(!(coerced17 !== undefined)){
if(dataType17 === "boolean" || data15 === null
              || (dataType17 === "string" && data15 && data15 == +data15 && !(data15 % 1))){
coerced17 = +data15;
}
else {
validate10.errors = [{instancePath:instancePath+"/maxParamLength",schemaPath:"#/properties/maxParamLength/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced17 !== undefined){
data15 = coerced17;
if(data !== undefined){
data["maxParamLength"] = coerced17;
}
}
}
var valid0 = _errs49 === errors;
if(valid0){
let data16 = data.onProtoPoisoning;
const _errs51 = errors;
if(typeof data16 !== "string"){
let dataType18 = typeof data16;
let coerced18 = undefined;
if(!(coerced18 !== undefined)){
if(dataType18 == "number" || dataType18 == "boolean"){
coerced18 = "" + data16;
}
else if(data16 === null){
coerced18 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/onProtoPoisoning",schemaPath:"#/properties/onProtoPoisoning/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced18 !== undefined){
data16 = coerced18;
if(data !== undefined){
data["onProtoPoisoning"] = coerced18;
}
}
}
var valid0 = _errs51 === errors;
if(valid0){
let data17 = data.onConstructorPoisoning;
const _errs53 = errors;
if(typeof data17 !== "string"){
let dataType19 = typeof data17;
let coerced19 = undefined;
if(!(coerced19 !== undefined)){
if(dataType19 == "number" || dataType19 == "boolean"){
coerced19 = "" + data17;
}
else if(data17 === null){
coerced19 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/onConstructorPoisoning",schemaPath:"#/properties/onConstructorPoisoning/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced19 !== undefined){
data17 = coerced19;
if(data !== undefined){
data["onConstructorPoisoning"] = coerced19;
}
}
}
var valid0 = _errs53 === errors;
if(valid0){
let data18 = data.pluginTimeout;
const _errs55 = errors;
if(!(((typeof data18 == "number") && (!(data18 % 1) && !isNaN(data18))) && (isFinite(data18)))){
let dataType20 = typeof data18;
let coerced20 = undefined;
if(!(coerced20 !== undefined)){
if(dataType20 === "boolean" || data18 === null
              || (dataType20 === "string" && data18 && data18 == +data18 && !(data18 % 1))){
coerced20 = +data18;
}
else {
validate10.errors = [{instancePath:instancePath+"/pluginTimeout",schemaPath:"#/properties/pluginTimeout/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced20 !== undefined){
data18 = coerced20;
if(data !== undefined){
data["pluginTimeout"] = coerced20;
}
}
}
var valid0 = _errs55 === errors;
if(valid0){
let data19 = data.requestIdHeader;
const _errs57 = errors;
const _errs58 = errors;
let valid6 = false;
const _errs59 = errors;
if(!(data19 === false)){
const err12 = {instancePath:instancePath+"/requestIdHeader",schemaPath:"#/properties/requestIdHeader/anyOf/0/enum",keyword:"enum",params:{allowedValues: schema11.properties.requestIdHeader.anyOf[0].enum},message:"must be equal to one of the allowed values"};
if(vErrors === null){
vErrors = [err12];
}
else {
vErrors.push(err12);
}
errors++;
}
var _valid3 = _errs59 === errors;
valid6 = valid6 || _valid3;
if(!valid6){
const _errs60 = errors;
if(typeof data19 !== "string"){
let dataType21 = typeof data19;
let coerced21 = undefined;
if(!(coerced21 !== undefined)){
if(dataType21 == "number" || dataType21 == "boolean"){
coerced21 = "" + data19;
}
else if(data19 === null){
coerced21 = "";
}
else {
const err13 = {instancePath:instancePath+"/requestIdHeader",schemaPath:"#/properties/requestIdHeader/anyOf/1/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err13];
}
else {
vErrors.push(err13);
}
errors++;
}
}
if(coerced21 !== undefined){
data19 = coerced21;
if(data !== undefined){
data["requestIdHeader"] = coerced21;
}
}
}
var _valid3 = _errs60 === errors;
valid6 = valid6 || _valid3;
}
if(!valid6){
const err14 = {instancePath:instancePath+"/requestIdHeader",schemaPath:"#/properties/requestIdHeader/anyOf",keyword:"anyOf",params:{},message:"must match a schema in anyOf"};
if(vErrors === null){
vErrors = [err14];
}
else {
vErrors.push(err14);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs58;
if(vErrors !== null){
if(_errs58){
vErrors.length = _errs58;
}
else {
vErrors = null;
}
}
}
var valid0 = _errs57 === errors;
if(valid0){
let data20 = data.requestIdLogLabel;
const _errs62 = errors;
if(typeof data20 !== "string"){
let dataType22 = typeof data20;
let coerced22 = undefined;
if(!(coerced22 !== undefined)){
if(dataType22 == "number" || dataType22 == "boolean"){
coerced22 = "" + data20;
}
else if(data20 === null){
coerced22 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/requestIdLogLabel",schemaPath:"#/properties/requestIdLogLabel/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced22 !== undefined){
data20 = coerced22;
if(data !== undefined){
data["requestIdLogLabel"] = coerced22;
}
}
}
var valid0 = _errs62 === errors;
if(valid0){
let data21 = data.http2SessionTimeout;
const _errs64 = errors;
if(!(((typeof data21 == "number") && (!(data21 % 1) && !isNaN(data21))) && (isFinite(data21)))){
let dataType23 = typeof data21;
let coerced23 = undefined;
if(!(coerced23 !== undefined)){
if(dataType23 === "boolean" || data21 === null
              || (dataType23 === "string" && data21 && data21 == +data21 && !(data21 % 1))){
coerced23 = +data21;
}
else {
validate10.errors = [{instancePath:instancePath+"/http2SessionTimeout",schemaPath:"#/properties/http2SessionTimeout/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];
return false;
}
}
if(coerced23 !== undefined){
data21 = coerced23;
if(data !== undefined){
data["http2SessionTimeout"] = coerced23;
}
}
}
var valid0 = _errs64 === errors;
if(valid0){
let data22 = data.exposeHeadRoutes;
const _errs66 = errors;
if(typeof data22 !== "boolean"){
let coerced24 = undefined;
if(!(coerced24 !== undefined)){
if(data22 === "false" || data22 === 0 || data22 === null){
coerced24 = false;
}
else if(data22 === "true" || data22 === 1){
coerced24 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/exposeHeadRoutes",schemaPath:"#/properties/exposeHeadRoutes/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced24 !== undefined){
data22 = coerced24;
if(data !== undefined){
data["exposeHeadRoutes"] = coerced24;
}
}
}
var valid0 = _errs66 === errors;
if(valid0){
let data23 = data.useSemicolonDelimiter;
const _errs68 = errors;
if(typeof data23 !== "boolean"){
let coerced25 = undefined;
if(!(coerced25 !== undefined)){
if(data23 === "false" || data23 === 0 || data23 === null){
coerced25 = false;
}
else if(data23 === "true" || data23 === 1){
coerced25 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/useSemicolonDelimiter",schemaPath:"#/properties/useSemicolonDelimiter/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced25 !== undefined){
data23 = coerced25;
if(data !== undefined){
data["useSemicolonDelimiter"] = coerced25;
}
}
}
var valid0 = _errs68 === errors;
if(valid0){
if(data.versioning !== undefined){
let data24 = data.versioning;
const _errs70 = errors;
if(errors === _errs70){
if(data24 && typeof data24 == "object" && !Array.isArray(data24)){
let missing1;
if(((data24.storage === undefined) && (missing1 = "storage")) || ((data24.deriveVersion === undefined) && (missing1 = "deriveVersion"))){
validate10.errors = [{instancePath:instancePath+"/versioning",schemaPath:"#/properties/versioning/required",keyword:"required",params:{missingProperty: missing1},message:"must have required property '"+missing1+"'"}];
return false;
}
}
else {
validate10.errors = [{instancePath:instancePath+"/versioning",schemaPath:"#/properties/versioning/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid0 = _errs70 === errors;
}
else {
var valid0 = true;
}
if(valid0){
if(data.constraints !== undefined){
let data25 = data.constraints;
const _errs73 = errors;
if(errors === _errs73){
if(data25 && typeof data25 == "object" && !Array.isArray(data25)){
for(const key2 in data25){
let data26 = data25[key2];
const _errs76 = errors;
if(errors === _errs76){
if(data26 && typeof data26 == "object" && !Array.isArray(data26)){
let missing2;
if(((((data26.name === undefined) && (missing2 = "name")) || ((data26.storage === undefined) && (missing2 = "storage"))) || ((data26.validate === undefined) && (missing2 = "validate"))) || ((data26.deriveConstraint === undefined) && (missing2 = "deriveConstraint"))){
validate10.errors = [{instancePath:instancePath+"/constraints/" + key2.replace(/~/g, "~0").replace(/\//g, "~1"),schemaPath:"#/properties/constraints/additionalProperties/required",keyword:"required",params:{missingProperty: missing2},message:"must have required property '"+missing2+"'"}];
return false;
}
else {
if(data26.name !== undefined){
let data27 = data26.name;
if(typeof data27 !== "string"){
let dataType26 = typeof data27;
let coerced26 = undefined;
if(!(coerced26 !== undefined)){
if(dataType26 == "number" || dataType26 == "boolean"){
coerced26 = "" + data27;
}
else if(data27 === null){
coerced26 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/constraints/" + key2.replace(/~/g, "~0").replace(/\//g, "~1")+"/name",schemaPath:"#/properties/constraints/additionalProperties/properties/name/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced26 !== undefined){
data27 = coerced26;
if(data26 !== undefined){
data26["name"] = coerced26;
}
}
}
}
}
}
else {
validate10.errors = [{instancePath:instancePath+"/constraints/" + key2.replace(/~/g, "~0").replace(/\//g, "~1"),schemaPath:"#/properties/constraints/additionalProperties/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid7 = _errs76 === errors;
if(!valid7){
break;
}
}
}
else {
validate10.errors = [{instancePath:instancePath+"/constraints",schemaPath:"#/properties/constraints/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid0 = _errs73 === errors;
}
else {
var valid0 = true;
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
}
else {
validate10.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
validate10.errors = vErrors;
return errors === 0;
}


module.exports.defaultInitOptions = {"connectionTimeout":0,"keepAliveTimeout":72000,"maxRequestsPerSocket":0,"requestTimeout":0,"bodyLimit":1048576,"caseSensitive":true,"allowUnsafeRegex":false,"disableRequestLogging":false,"jsonShorthand":true,"ignoreTrailingSlash":false,"ignoreDuplicateSlashes":false,"maxParamLength":100,"onProtoPoisoning":"error","onConstructorPoisoning":"error","pluginTimeout":10000,"requestIdHeader":"request-id","requestIdLogLabel":"reqId","http2SessionTimeout":72000,"exposeHeadRoutes":true,"useSemicolonDelimiter":true}
