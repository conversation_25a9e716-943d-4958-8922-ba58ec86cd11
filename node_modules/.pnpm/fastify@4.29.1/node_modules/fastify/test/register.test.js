'use strict'

/* eslint no-prototype-builtins: 0 */

const t = require('tap')
const test = t.test
const sget = require('simple-get').concat
const Fastify = require('..')

test('register', t => {
  t.plan(17)

  const fastify = Fastify()

  fastify.register(function (instance, opts, done) {
    t.not(instance, fastify)
    t.ok(fastify.isPrototypeOf(instance))

    t.equal(typeof opts, 'object')
    t.equal(typeof done, 'function')

    instance.get('/first', function (req, reply) {
      reply.send({ hello: 'world' })
    })
    done()
  })

  fastify.register(function (instance, opts, done) {
    t.not(instance, fastify)
    t.ok(fastify.isPrototypeOf(instance))

    t.equal(typeof opts, 'object')
    t.equal(typeof done, 'function')

    instance.get('/second', function (req, reply) {
      reply.send({ hello: 'world' })
    })
    done()
  })

  fastify.listen({ port: 0 }, err => {
    t.error(err)
    t.teardown(() => { fastify.close() })

    makeRequest('first')
    makeRequest('second')
  })

  function makeRequest (path) {
    sget({
      method: 'GET',
      url: 'http://localhost:' + fastify.server.address().port + '/' + path
    }, (err, response, body) => {
      t.error(err)
      t.equal(response.statusCode, 200)
      t.equal(response.headers['content-length'], '' + body.length)
      t.same(JSON.parse(body), { hello: 'world' })
    })
  }
})

test('internal route declaration should pass the error generated by the register to the done handler / 1', t => {
  t.plan(1)
  const fastify = Fastify()

  fastify.register((instance, opts, done) => {
    done(new Error('kaboom'))
  })

  fastify.get('/', (req, reply) => {
    reply.send({ hello: 'world' })
  })

  fastify.listen({ port: 0 }, err => {
    fastify.close()
    t.equal(err.message, 'kaboom')
  })
})

test('internal route declaration should pass the error generated by the register to the done handler / 2', t => {
  t.plan(2)
  const fastify = Fastify()

  fastify.register((instance, opts, done) => {
    done(new Error('kaboom'))
  })

  fastify.get('/', (req, reply) => {
    reply.send({ hello: 'world' })
  })

  fastify.after(err => {
    t.equal(err.message, 'kaboom')
  })

  fastify.listen({ port: 0 }, err => {
    fastify.close()
    t.error(err)
  })
})

test('awaitable register and after', async t => {
  const fastify = Fastify()
  let first = false
  let second = false
  let third = false

  await fastify.register(async (instance, opts) => {
    first = true
  })

  t.equal(first, true)

  fastify.register(async (instance, opts) => {
    second = true
  })

  await fastify.after()
  t.equal(second, true)

  fastify.register(async (instance, opts) => {
    third = true
  })

  await fastify.ready()
  t.equal(third, true)
})

function thenableRejects (t, promise, error) {
  return t.rejects(async () => { await promise }, error)
}

test('awaitable register error handling', async t => {
  const fastify = Fastify()

  const e = new Error('kaboom')

  await thenableRejects(t, fastify.register(async (instance, opts) => {
    throw e
  }), e)

  fastify.register(async (instance, opts) => {
    t.fail('should not be executed')
  })

  await t.rejects(fastify.after(), e)

  fastify.register(async (instance, opts) => {
    t.fail('should not be executed')
  })

  await thenableRejects(t, fastify.ready(), e)
})

test('awaitable after error handling', async t => {
  const fastify = Fastify()

  const e = new Error('kaboom')

  fastify.register(async (instance, opts) => {
    throw e
  })

  fastify.register(async (instance, opts) => {
    t.fail('should not be executed')
  })

  await t.rejects(fastify.after(), e)

  fastify.register(async (instance, opts) => {
    t.fail('should not be executed')
  })

  await t.rejects(fastify.ready())
})

test('chainable register', async t => {
  t.plan(3)

  const fastify = Fastify()

  fastify.register(async () => {
    t.pass('first loaded')
  }).register(async () => {
    t.pass('second loaded')
  }).register(async () => {
    t.pass('third loaded')
  })

  await fastify.ready()
})
