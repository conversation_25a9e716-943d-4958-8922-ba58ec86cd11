'use strict'

const t = require('tap')
const { test, before } = t
const sget = require('simple-get').concat
const fastify = require('..')
const helper = require('./helper')

const sgetForwardedRequest = (app, forHeader, path, protoHeader) => {
  const headers = {
    'X-Forwarded-For': forHeader,
    'X-Forwarded-Host': 'example.com'
  }
  if (protoHeader) {
    headers['X-Forwarded-Proto'] = protoHeader
  }
  sget({
    method: 'GET',
    headers,
    url: 'http://localhost:' + app.server.address().port + path
  }, () => {})
}

const testRequestValues = (t, req, options) => {
  if (options.ip) {
    t.ok(req.ip, 'ip is defined')
    t.equal(req.ip, options.ip, 'gets ip from x-forwarded-for')
  }
  if (options.hostname) {
    t.ok(req.hostname, 'hostname is defined')
    t.equal(req.hostname, options.hostname, 'gets hostname from x-forwarded-host')
  }
  if (options.ips) {
    t.same(req.ips, options.ips, 'gets ips from x-forwarded-for')
  }
  if (options.protocol) {
    t.ok(req.protocol, 'protocol is defined')
    t.equal(req.protocol, options.protocol, 'gets protocol from x-forwarded-proto')
  }
}

let localhost
before(async function () {
  [localhost] = await helper.getLoopbackHost()
})

test('trust proxy, not add properties to node req', (t) => {
  t.plan(8)
  const app = fastify({
    trustProxy: true
  })
  app.get('/trustproxy', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', hostname: 'example.com' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  app.get('/trustproxychain', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', ips: [localhost, '*******', '*******'] })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '*******', '/trustproxy')
    sgetForwardedRequest(app, '*******, *******', '/trustproxychain')
  })
})

test('trust proxy chain', (t) => {
  t.plan(3)
  const app = fastify({
    trustProxy: [localhost, '***********']
  })

  app.get('/trustproxychain', function (req, reply) {
    testRequestValues(t, req, { ip: '*******' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '***********, *******', '/trustproxychain')
  })
})

test('trust proxy function', (t) => {
  t.plan(3)
  const app = fastify({
    trustProxy: (address) => address === localhost
  })
  app.get('/trustproxyfunc', function (req, reply) {
    testRequestValues(t, req, { ip: '*******' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '*******', '/trustproxyfunc')
  })
})

test('trust proxy number', (t) => {
  t.plan(4)
  const app = fastify({
    trustProxy: 1
  })
  app.get('/trustproxynumber', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', ips: [localhost, '*******'] })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '*******, *******', '/trustproxynumber')
  })
})

test('trust proxy IP addresses', (t) => {
  t.plan(4)
  const app = fastify({
    trustProxy: `${localhost}, *******`
  })
  app.get('/trustproxyipaddrs', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', ips: [localhost, '*******'] })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '*******, *******, *******', '/trustproxyipaddrs')
  })
})

test('trust proxy protocol', (t) => {
  t.plan(13)
  const app = fastify({
    trustProxy: true
  })
  app.get('/trustproxyprotocol', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', protocol: 'lorem' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })
  app.get('/trustproxynoprotocol', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', protocol: 'http' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })
  app.get('/trustproxyprotocols', function (req, reply) {
    testRequestValues(t, req, { ip: '*******', protocol: 'dolor' })
    reply.code(200).send({ ip: req.ip, hostname: req.hostname })
  })

  t.teardown(app.close.bind(app))

  app.listen({ port: 0 }, (err) => {
    app.server.unref()
    t.error(err)
    sgetForwardedRequest(app, '*******', '/trustproxyprotocol', 'lorem')
    sgetForwardedRequest(app, '*******', '/trustproxynoprotocol')
    sgetForwardedRequest(app, '*******', '/trustproxyprotocols', 'ipsum, dolor')
  })
})
