'use strict'

const t = require('tap')
const test = t.test
const sget = require('simple-get').concat
const fastify = require('..')()

const bodySample = `<?xml version="1.0" encoding="utf-8" ?>
        <D:propertyupdate xmlns:D="DAV:"
          xmlns:Z="http://ns.example.com/standards/z39.50/">
          <D:set>
            <D:prop>
              <Z:Authors>
                <Z:Author><PERSON></Z:Author>
                <Z:Author><PERSON></Z:Author>
              </Z:Authors>
            </D:prop>
          </D:set>
          <D:remove>
            <D:prop>
              <Z:Copyright-Owner/>
            </D:prop>
          </D:remove>
        </D:propertyupdate>`

test('shorthand - proppatch', t => {
  t.plan(1)
  try {
    fastify.route({
      method: 'PROPPATCH',
      url: '*',
      handler: function (req, reply) {
        reply
          .code(207)
          .send(`<?xml version="1.0" encoding="utf-8" ?>
            <D:multistatus xmlns:D="DAV:"
              xmlns:Z="http://ns.example.com/standards/z39.50/">
              <D:response>
                <D:href>http://www.example.com/bar.html</D:href>
                <D:propstat>
                  <D:prop>
                    <Z:Authors/>
                  </D:prop>
                  <D:status>HTTP/1.1 424 Failed Dependency</D:status>
                </D:propstat>
                <D:propstat>
                  <D:prop>
                    <Z:Copyright-Owner/>
                  </D:prop>
                  <D:status>HTTP/1.1 409 Conflict</D:status>
                </D:propstat>
                <D:responsedescription> Copyright Owner cannot be deleted or altered.</D:responsedescription>
              </D:response>
            </D:multistatus>`
          )
      }
    })
    t.pass()
  } catch (e) {
    t.fail()
  }
})

fastify.listen({ port: 0 }, err => {
  t.error(err)
  t.teardown(() => { fastify.close() })

  // the body test uses a text/plain content type instead of application/xml because it requires
  // a specific content type parser
  test('request with body - proppatch', t => {
    t.plan(3)
    sget({
      url: `http://localhost:${fastify.server.address().port}/test/a.txt`,
      headers: { 'content-type': 'text/plain' },
      body: bodySample,
      method: 'PROPPATCH'
    }, (err, response, body) => {
      t.error(err)
      t.equal(response.statusCode, 207)
      t.equal(response.headers['content-length'], '' + body.length)
    })
  })

  test('request with body and no content type (415 error) - proppatch', t => {
    t.plan(3)
    sget({
      url: `http://localhost:${fastify.server.address().port}/test/a.txt`,
      body: bodySample,
      method: 'PROPPATCH'
    }, (err, response, body) => {
      t.error(err)
      t.equal(response.statusCode, 415)
      t.equal(response.headers['content-length'], '' + body.length)
    })
  })

  test('request without body - proppatch', t => {
    t.plan(3)
    sget({
      url: `http://localhost:${fastify.server.address().port}/test/a.txt`,
      method: 'PROPPATCH'
    }, (err, response, body) => {
      t.error(err)
      t.equal(response.statusCode, 207)
      t.equal(response.headers['content-length'], '' + body.length)
    })
  })
})
