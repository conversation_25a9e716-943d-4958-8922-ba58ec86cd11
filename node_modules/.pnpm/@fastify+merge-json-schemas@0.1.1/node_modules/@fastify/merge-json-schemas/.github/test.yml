name: Run Tests

on: [push, pull_request]

permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [18.x, 20.x]
        os: [ubuntu-latest, windows-latest, macOS-latest] 

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install
      run: |
        npm install

    - name: Test
      run: |
        npm run test
