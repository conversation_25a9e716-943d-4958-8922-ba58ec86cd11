version: '3.8'

services:
  # The backend service for your application
  backend:
    build: .  # Assumes you have a Dockerfile in the same directory
    ports:
      - "3000:3000" # Expose the backend port (matching app.js port 3000)
    depends_on:
      - postgres # Ensures the database container starts before the backend
    volumes:
      - .:/app # Mount the current directory into the container for live updates
    environment:
      # These environment variables link to the postgres service
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=mydb

  # The PostgreSQL database service
  postgres:
    image: postgres:13
    restart: always
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=mydb
    volumes:
      - postgres_data:/var/lib/postgresql/data # Persist data in a named volume

  # The pgAdmin service for database management
  pgadmin:
    image: dpage/pgadmin4
    restart: always
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80" # Expose pgAdmin on port 5050
    depends_on:
      - postgres
    links:
      - postgres:postgres_server

volumes:
  postgres_data:
