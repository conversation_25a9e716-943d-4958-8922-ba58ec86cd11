CREATE OR <PERSON><PERSON>LACE FUNCTION audit.if_audit_table()
R<PERSON>URNS BOOLEAN LANGUAGE plpgsql AS $$
BEGIN
    -- Do not audit the audit log itself
    IF TG_TABLE_SCHEMA = 'audit' THEN
        RETURN FALSE;
    END IF;
    RETURN TRUE;
END;
$$;

CREATE OR REPLACE FUNCTION audit.audit_trigger_fn()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
    pk_col   TEXT;
    pk_val   TEXT;
BEGIN
    IF NOT audit.if_audit_table() THEN
        RETURN NULL;
    END IF;

    -- Heuristic: use the first column with PRIMARY KEY
    SELECT kcu.column_name INTO pk_col
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
     AND tc.table_schema    = kcu.table_schema
    WHERE tc.table_schema = TG_TABLE_SCHEMA
      AND tc.table_name   = TG_TABLE_NAME
      AND tc.constraint_type = 'PRIMARY KEY'
    LIMIT 1;

    IF pk_col IS NULL THEN
        RAISE WARNING 'No PK on %.% – skipping audit', TG_TABLE_SCHEMA, TG_TABLE_NAME;
        RETURN NULL;
    END IF;

    IF TG_OP IN ('UPDATE','DELETE') THEN
        pk_val := (OLD)::jsonb->>pk_col;
    ELSE
        pk_val := (NEW)::jsonb->>pk_col;
    END IF;

    INSERT INTO audit.audit_log(table_name, operation, pk_value, old_row, new_row)
    VALUES (
        TG_TABLE_SCHEMA||'.'||TG_TABLE_NAME,
        TG_OP,
        pk_val,
        CASE WHEN TG_OP IN ('UPDATE','DELETE') THEN to_jsonb(OLD) END,
        CASE WHEN TG_OP IN ('INSERT','UPDATE') THEN to_jsonb(NEW) END
    );

    RETURN NULL;
END;
$$;

-- Automatically create triggers for every existing & future table
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE schemaname NOT IN ('pg_catalog','information_schema','audit')
    LOOP
        EXECUTE format($f$
            CREATE TRIGGER audit_trg
            AFTER INSERT OR UPDATE OR DELETE ON %I.%I
            FOR EACH ROW EXECUTE PROCEDURE audit.audit_trigger_fn();
        $f$, r.schemaname, r.tablename);
    END LOOP;
END;
$$;

-- Event trigger so NEW tables get the trigger automatically
CREATE OR REPLACE FUNCTION audit.create_trigger_on_new_table()
RETURNS EVENT_TRIGGER LANGUAGE plpgsql AS $$
DECLARE
    obj RECORD;
BEGIN
    FOR obj IN SELECT * FROM pg_event_trigger_ddl_commands()
               WHERE command_tag='CREATE TABLE'
    LOOP
        IF obj.schema_name NOT IN ('pg_catalog','information_schema','audit') THEN
            EXECUTE format($f$
                CREATE TRIGGER audit_trg
                AFTER INSERT OR UPDATE OR DELETE ON %I.%I
                FOR EACH ROW EXECUTE PROCEDURE audit.audit_trigger_fn();
            $f$, obj.schema_name, obj.object_name);
        END IF;
    END LOOP;
END;
$$;

CREATE EVENT TRIGGER trig_auto_audit
    ON ddl_command_end
    WHEN TAG IN ('CREATE TABLE')
    EXECUTE PROCEDURE audit.create_trigger_on_new_table();